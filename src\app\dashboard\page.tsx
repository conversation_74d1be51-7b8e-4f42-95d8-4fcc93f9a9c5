'use client'

import { useEffect, useState, useCallback } from 'react'
import DashboardLayout from '@/components/DashboardLayout'
import AnimatedCard from '@/components/AnimatedCard'
import { LoadingButton } from '@/components/LoadingSpinner'
import { useNotifications } from '@/components/AnimatedNotification'
import { LazyCharts } from '@/components/optimization/LazyLoader'
import { LazyOnScroll } from '@/components/optimization/LazyLoader'
import PerformanceMonitor from '@/components/optimization/PerformanceMonitor'
import { useHydrationSafe } from '@/hooks/useHydrationSafe'
import {
  Package,
  Users,
  CreditCard,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  Clock,
  DollarSign,
  ArrowUpRight,
  Activity,
  Calendar,
  Zap,
  Target,
  BarChart3,
  <PERSON><PERSON>hart
} from 'lucide-react'

interface DashboardStats {
  totalProducts: number
  totalCustomers: number
  totalDebts: number
  totalRevenue: number
}

interface RecentActivity {
  id: string
  type: 'debt' | 'payment' | 'product'
  description: string
  amount?: number
  timestamp: string
}

interface PaymentWithCustomer {
  id: string
  amount: number
  dateOfPayment: string
  customer: {
    firstName: string
    lastName: string
  }
}

interface DebtWithCustomer {
  id: string
  totalAmount: number
  dateOfDebt: string
  customer: {
    firstName: string
    lastName: string
  }
}

export default function Dashboard() {
  const [stats, setStats] = useState<DashboardStats>({
    totalProducts: 0,
    totalCustomers: 0,
    totalDebts: 0,
    totalRevenue: 0,
  })
  const [loading, setLoading] = useState(true)
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([])
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const isHydrated = useHydrationSafe()
  const { showSuccess, showError } = useNotifications()

  // Sample chart data - In production, this would come from your API
  const salesData = [
    { date: 'Mon', sales: 85, target: 100, revenue: 2100 },
    { date: 'Tue', sales: 92, target: 100, revenue: 2300 },
    { date: 'Wed', sales: 78, target: 100, revenue: 1950 },
    { date: 'Thu', sales: 95, target: 100, revenue: 2375 },
    { date: 'Fri', sales: 88, target: 100, revenue: 2200 },
    { date: 'Sat', sales: 110, target: 100, revenue: 2750 },
    { date: 'Sun', sales: 75, target: 100, revenue: 1875 },
  ]

  const revenueData = [
    { month: 'Jan', revenue: 45000, profit: 12000, expenses: 33000 },
    { month: 'Feb', revenue: 52000, profit: 15000, expenses: 37000 },
    { month: 'Mar', revenue: 48000, profit: 13500, expenses: 34500 },
    { month: 'Apr', revenue: 61000, profit: 18000, expenses: 43000 },
    { month: 'May', revenue: 55000, profit: 16500, expenses: 38500 },
    { month: 'Jun', revenue: 67000, profit: 20000, expenses: 47000 },
  ]

  const inventoryData = [
    { category: 'Beverages', value: 150, status: 'in-stock' as const, color: '#3b82f6' },
    { category: 'Snacks', value: 89, status: 'low-stock' as const, color: '#f59e0b' },
    { category: 'Household', value: 45, status: 'in-stock' as const, color: '#10b981' },
    { category: 'Personal Care', value: 23, status: 'out-of-stock' as const, color: '#ef4444' },
    { category: 'Condiments', value: 67, status: 'in-stock' as const, color: '#8b5cf6' },
  ]

  const customerData = [
    { month: 'Jan', newCustomers: 12, activeCustomers: 45, churnedCustomers: 2, satisfaction: 4.2 },
    { month: 'Feb', newCustomers: 18, activeCustomers: 58, churnedCustomers: 3, satisfaction: 4.3 },
    { month: 'Mar', newCustomers: 15, activeCustomers: 67, churnedCustomers: 1, satisfaction: 4.5 },
    { month: 'Apr', newCustomers: 22, activeCustomers: 78, churnedCustomers: 4, satisfaction: 4.4 },
    { month: 'May', newCustomers: 19, activeCustomers: 85, churnedCustomers: 2, satisfaction: 4.6 },
    { month: 'Jun', newCustomers: 25, activeCustomers: 95, churnedCustomers: 3, satisfaction: 4.7 },
  ]

  // Enhanced data fetching with auto-refresh
  const fetchStats = useCallback(async (isRefresh = false) => {
    if (isRefresh) {
      setIsRefreshing(true)
    } else {
      setLoading(true)
    }

    try {
      // Fetch all data concurrently for better performance
      const [productsRes, customersRes, debtsRes, paymentsRes] = await Promise.all([
        fetch('/api/products'),
        fetch('/api/customers'),
        fetch('/api/debts'),
        fetch('/api/payments')
      ])

      const [products, customers, debts, payments] = await Promise.all([
        productsRes.json(),
        customersRes.json(),
        debtsRes.json(),
        paymentsRes.json()
      ])

      const totalRevenue = payments.reduce((sum: number, payment: PaymentWithCustomer) => sum + payment.amount, 0)
      const totalDebts = debts.reduce((sum: number, debt: DebtWithCustomer) => sum + debt.totalAmount, 0)

      setStats({
        totalProducts: products.length,
        totalCustomers: customers.length,
        totalDebts,
        totalRevenue,
      })

      // Generate recent activity with better sorting
      const activities: RecentActivity[] = [
        ...payments.slice(0, 3).map((payment: PaymentWithCustomer) => ({
          id: payment.id,
          type: 'payment' as const,
          description: `Payment received from ${payment.customer.firstName} ${payment.customer.lastName}`,
          amount: payment.amount,
          timestamp: payment.dateOfPayment,
        })),
        ...debts.slice(0, 2).map((debt: DebtWithCustomer) => ({
          id: debt.id,
          type: 'debt' as const,
          description: `New debt recorded for ${debt.customer.firstName} ${debt.customer.lastName}`,
          amount: debt.totalAmount,
          timestamp: debt.dateOfDebt,
        })),
      ].sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()).slice(0, 5)

      setRecentActivity(activities)
      setLastUpdated(new Date())

      if (isRefresh) {
        showSuccess('Data Updated', 'Dashboard data has been refreshed successfully!')
      }
    } catch (error) {
      console.error('Error fetching dashboard stats:', error)
      if (isRefresh) {
        showError('Update Failed', 'Failed to refresh dashboard data. Please try again.')
      }
    } finally {
      setLoading(false)
      setIsRefreshing(false)
    }
  }, [showSuccess, showError])

  useEffect(() => {
    fetchStats()

    // Auto-refresh every 5 minutes
    const interval = setInterval(() => {
      fetchStats(true)
    }, 5 * 60 * 1000)

    return () => clearInterval(interval)
  }, [fetchStats])

  // Manual refresh function
  const handleRefresh = () => {
    fetchStats(true)
  }

  const statCards = [
    {
      title: 'Total Products',
      value: stats.totalProducts,
      icon: Package,
      gradient: 'from-blue-500 via-blue-600 to-blue-700',
      bgColor: 'bg-gradient-to-br from-blue-50 to-blue-100',
      textColor: 'text-blue-700',
      iconBg: 'bg-gradient-to-br from-blue-500 to-blue-600',
      change: '+12%',
      changeType: 'positive' as const,
      trend: [65, 78, 82, 95, 88, 92, 98],
      description: 'Active inventory items',
    },
    {
      title: 'Total Customers',
      value: stats.totalCustomers,
      icon: Users,
      gradient: 'from-emerald-500 via-emerald-600 to-emerald-700',
      bgColor: 'bg-gradient-to-br from-emerald-50 to-emerald-100',
      textColor: 'text-emerald-700',
      iconBg: 'bg-gradient-to-br from-emerald-500 to-emerald-600',
      change: '+8%',
      changeType: 'positive' as const,
      trend: [45, 52, 48, 61, 55, 67, 73],
      description: 'Registered customers',
    },
    {
      title: 'Outstanding Debts',
      value: `₱${stats.totalDebts.toLocaleString()}`,
      icon: CreditCard,
      gradient: 'from-amber-500 via-orange-500 to-red-500',
      bgColor: 'bg-gradient-to-br from-amber-50 to-orange-100',
      textColor: 'text-orange-700',
      iconBg: 'bg-gradient-to-br from-amber-500 to-orange-500',
      change: '-5%',
      changeType: 'negative' as const,
      trend: [85, 78, 82, 75, 68, 72, 65],
      description: 'Pending collections',
    },
    {
      title: 'Total Revenue',
      value: `₱${stats.totalRevenue.toLocaleString()}`,
      icon: TrendingUp,
      gradient: 'from-violet-500 via-purple-600 to-purple-700',
      bgColor: 'bg-gradient-to-br from-violet-50 to-purple-100',
      textColor: 'text-purple-700',
      iconBg: 'bg-gradient-to-br from-violet-500 to-purple-600',
      change: '+23%',
      changeType: 'positive' as const,
      trend: [120, 135, 148, 162, 158, 175, 189],
      description: 'Monthly earnings',
    },
  ]

  const formatDate = (dateString: string) => {
    if (!isHydrated) return 'Loading...'
    try {
      const date = new Date(dateString)
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    } catch {
      return 'Invalid date'
    }
  }

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'payment':
        return <DollarSign className="h-4 w-4 text-green-600" />
      case 'debt':
        return <CreditCard className="h-4 w-4 text-orange-600" />
      case 'product':
        return <Package className="h-4 w-4 text-blue-600" />
      default:
        return <Clock className="h-4 w-4 text-gray-600" />
    }
  }

  return (
    <DashboardLayout
      title="Dashboard"
      subtitle={`Welcome back! Here's what's happening with your store today.${lastUpdated && isHydrated ? ` • Last updated: ${lastUpdated.toLocaleTimeString()}` : ''}`}
      actions={
        <div className="flex items-center space-x-2 sm:space-x-3">
          <LoadingButton
            loading={isRefreshing}
            onClick={handleRefresh}
            variant="secondary"
            className="animate-fade-in stagger-1"
          >
            <Activity className="h-4 w-4" />
            <span className="hidden sm:inline">Refresh</span>
          </LoadingButton>
          <button className="btn-primary btn-interactive animate-fade-in stagger-2">
            <span className="hidden sm:inline">Quick Sale</span>
            <span className="sm:hidden">Sale</span>
          </button>
        </div>
      }
    >
      {/* Enhanced Professional Stats Cards - Mobile Optimized */}
      <div className="w-full mobile-content animate-fade-in stagger-1">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8">
        {statCards.map((card, index) => (
          <AnimatedCard
            key={index}
            title={card.title}
            value={card.value}
            description={card.description}
            icon={card.icon}
            gradient={card.gradient}
            bgColor={card.bgColor}
            textColor={card.textColor}
            iconBg={card.iconBg}
            change={card.change}
            changeType={card.changeType}
            trend={card.trend}
            delay={index * 100}
            loading={loading}
          />
        ))}
        </div>
      </div>

      <div className="w-full mobile-content animate-fade-in stagger-2">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6">
        {/* Enhanced Quick Actions - Mobile Optimized */}
        <div className="lg:col-span-2 order-2 lg:order-1">
          <div className="card-elevated p-6 hover-lift">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h3 className="text-xl font-bold text-gray-900">Quick Actions</h3>
                <p className="text-sm text-gray-600 mt-1">Streamline your daily operations</p>
              </div>
              <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-2 rounded-lg">
                <Zap className="h-5 w-5 text-white" />
              </div>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
              <a
                href="/products/new"
                className="group relative block p-5 bg-gradient-to-br from-blue-50 via-blue-100 to-indigo-100 hover:from-blue-100 hover:via-blue-200 hover:to-indigo-200 rounded-2xl transition-all duration-300 border border-blue-200 hover:border-blue-300 hover-lift overflow-hidden animate-scale-in stagger-1"
              >
                <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full opacity-10 transform translate-x-8 -translate-y-8"></div>
                <div className="relative flex items-center space-x-4">
                  <div className="bg-gradient-to-br from-blue-500 to-blue-600 p-3 rounded-xl shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-transform duration-300">
                    <Package className="h-6 w-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <div className="font-bold text-blue-900 text-lg">Add Product</div>
                    <div className="text-sm text-blue-700 mt-1">Expand your inventory</div>
                  </div>
                  <ArrowUpRight className="h-5 w-5 text-blue-600 group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform duration-300" />
                </div>
              </a>

              <a
                href="/customers/new"
                className="group relative block p-5 bg-gradient-to-br from-emerald-50 via-emerald-100 to-green-100 hover:from-emerald-100 hover:via-emerald-200 hover:to-green-200 rounded-2xl transition-all duration-300 border border-emerald-200 hover:border-emerald-300 hover-lift overflow-hidden animate-scale-in stagger-2"
              >
                <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-emerald-400 to-emerald-600 rounded-full opacity-10 transform translate-x-8 -translate-y-8"></div>
                <div className="relative flex items-center space-x-4">
                  <div className="bg-gradient-to-br from-emerald-500 to-emerald-600 p-3 rounded-xl shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-transform duration-300">
                    <Users className="h-6 w-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <div className="font-bold text-emerald-900 text-lg">Add Customer</div>
                    <div className="text-sm text-emerald-700 mt-1">Register new customer</div>
                  </div>
                  <ArrowUpRight className="h-5 w-5 text-emerald-600 group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform duration-300" />
                </div>
              </a>

              <a
                href="/debts/new"
                className="group relative block p-5 bg-gradient-to-br from-amber-50 via-orange-100 to-red-100 hover:from-amber-100 hover:via-orange-200 hover:to-red-200 rounded-2xl transition-all duration-300 border border-orange-200 hover:border-orange-300 hover-lift overflow-hidden animate-scale-in stagger-3"
              >
                <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-orange-400 to-red-500 rounded-full opacity-10 transform translate-x-8 -translate-y-8"></div>
                <div className="relative flex items-center space-x-4">
                  <div className="bg-gradient-to-br from-orange-500 to-red-500 p-3 rounded-xl shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-transform duration-300">
                    <CreditCard className="h-6 w-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <div className="font-bold text-orange-900 text-lg">Record Debt</div>
                    <div className="text-sm text-orange-700 mt-1">Track customer purchases</div>
                  </div>
                  <ArrowUpRight className="h-5 w-5 text-orange-600 group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform duration-300" />
                </div>
              </a>

              <a
                href="/payments/new"
                className="group relative block p-5 bg-gradient-to-br from-violet-50 via-purple-100 to-indigo-100 hover:from-violet-100 hover:via-purple-200 hover:to-indigo-200 rounded-2xl transition-all duration-300 border border-purple-200 hover:border-purple-300 hover-lift overflow-hidden animate-scale-in stagger-4"
              >
                <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-purple-400 to-indigo-600 rounded-full opacity-10 transform translate-x-8 -translate-y-8"></div>
                <div className="relative flex items-center space-x-4">
                  <div className="bg-gradient-to-br from-purple-500 to-indigo-600 p-3 rounded-xl shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-transform duration-300">
                    <DollarSign className="h-6 w-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <div className="font-bold text-purple-900 text-lg">Record Payment</div>
                    <div className="text-sm text-purple-700 mt-1">Process customer payment</div>
                  </div>
                  <ArrowUpRight className="h-5 w-5 text-purple-600 group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform duration-300" />
                </div>
              </a>
            </div>
          </div>
        </div>

        {/* Enhanced Recent Activity - Mobile Optimized */}
        <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-4 sm:p-6 hover:shadow-md transition-shadow duration-300 order-1 lg:order-2">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-xl font-bold text-gray-900">Recent Activity</h3>
              <p className="text-sm text-gray-600 mt-1">Latest store operations</p>
            </div>
            <div className="bg-gradient-to-r from-green-500 to-blue-600 p-2 rounded-lg">
              <Activity className="h-5 w-5 text-white" />
            </div>
          </div>
          <div className="space-y-3">
            {loading ? (
              <div className="space-y-3">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="animate-pulse flex items-center space-x-3 p-4 bg-gray-50 rounded-xl">
                    <div className="w-10 h-10 bg-gray-200 rounded-lg"></div>
                    <div className="flex-1">
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  </div>
                ))}
              </div>
            ) : recentActivity.length > 0 ? (
              recentActivity.map((activity) => (
                <div key={activity.id} className="group flex items-start space-x-4 p-4 bg-gradient-to-r from-gray-50 to-gray-100 hover:from-blue-50 hover:to-indigo-50 rounded-xl transition-all duration-300 border border-transparent hover:border-blue-200">
                  <div className="flex-shrink-0 mt-1 p-2 bg-white rounded-lg shadow-sm group-hover:shadow-md transition-shadow duration-300">
                    {getActivityIcon(activity.type)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-semibold text-gray-900 truncate group-hover:text-blue-900 transition-colors duration-300">
                      {activity.description}
                    </p>
                    <div className="flex items-center justify-between mt-2">
                      <p className="text-xs text-gray-500 flex items-center space-x-1">
                        <Clock className="h-3 w-3" />
                        <span>{formatDate(activity.timestamp)}</span>
                      </p>
                      {activity.amount && (
                        <p className="text-sm font-bold text-gray-900 bg-white px-2 py-1 rounded-lg shadow-sm">
                          ₱{activity.amount.toLocaleString()}
                        </p>
                      )}
                    </div>
                  </div>
                  <div className="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <ArrowUpRight className="h-4 w-4 text-blue-600" />
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8">
                <div className="bg-gray-100 rounded-full p-4 w-16 h-16 mx-auto mb-4">
                  <Activity className="h-8 w-8 text-gray-400" />
                </div>
                <p className="text-sm text-gray-500">No recent activity to display</p>
                <p className="text-xs text-gray-400 mt-1">Activity will appear here as you use the system</p>
              </div>
            )}
          </div>
        </div>
        </div>
      </div>

      {/* Professional Analytics Charts */}
      <div className="w-full mobile-content animate-fade-in stagger-3">
        <div className="mt-8 space-y-8">
          {/* Sales Performance Chart - Lazy Loaded */}
          <LazyOnScroll>
            <LazyCharts.SalesChart data={salesData} />
          </LazyOnScroll>

          {/* Revenue and Inventory Charts - Lazy Loaded */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <LazyOnScroll>
              <LazyCharts.RevenueChart data={revenueData} />
            </LazyOnScroll>
            <LazyOnScroll>
              <LazyCharts.InventoryChart data={inventoryData} />
            </LazyOnScroll>
          </div>

          {/* Customer Analytics - Lazy Loaded */}
          <LazyOnScroll>
            <LazyCharts.CustomerChart data={customerData} />
          </LazyOnScroll>
        </div>
      </div>

      {/* Enhanced Business Insights - Mobile Optimized */}
      <div className="w-full mobile-content">
        <div className="mt-6 sm:mt-8 grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
        {/* Performance Chart */}
        <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow duration-300">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-xl font-bold text-gray-900">Weekly Performance</h3>
              <p className="text-sm text-gray-600 mt-1">Sales vs targets</p>
            </div>
            <div className="bg-gradient-to-r from-blue-500 to-green-500 p-2 rounded-lg">
              <BarChart3 className="h-5 w-5 text-white" />
            </div>
          </div>
          <div className="space-y-4">
            {/* Interactive Enhanced Chart Data */}
            {[
              { day: 'Mon', sales: 85, target: 100, color: 'from-blue-400 to-blue-600', revenue: 2100 },
              { day: 'Tue', sales: 92, target: 100, color: 'from-green-400 to-green-600', revenue: 2300 },
              { day: 'Wed', sales: 78, target: 100, color: 'from-yellow-400 to-orange-500', revenue: 1950 },
              { day: 'Thu', sales: 95, target: 100, color: 'from-green-400 to-green-600', revenue: 2375 },
              { day: 'Fri', sales: 88, target: 100, color: 'from-blue-400 to-blue-600', revenue: 2200 },
              { day: 'Sat', sales: 110, target: 100, color: 'from-emerald-400 to-emerald-600', revenue: 2750 },
              { day: 'Sun', sales: 75, target: 100, color: 'from-red-400 to-red-600', revenue: 1875 },
            ].map((data, index) => (
              <div key={index} className="group cursor-pointer hover:bg-gray-50 p-3 rounded-xl transition-all duration-300">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 text-sm font-bold text-gray-800">{data.day}</div>
                    <div className="text-xs text-gray-500">Target: {data.target}%</div>
                  </div>
                  <div className="text-right">
                    <div className={`text-sm font-bold ${
                      data.sales >= data.target ? 'text-green-600' : 'text-orange-600'
                    }`}>
                      {data.sales}%
                    </div>
                    <div className="text-xs text-gray-500 group-hover:text-gray-700 transition-colors">
                      ₱{data.revenue.toLocaleString()}
                    </div>
                  </div>
                </div>
                <div className="relative">
                  <div className="flex-1 bg-gray-200 rounded-full h-4 relative overflow-hidden group-hover:h-5 transition-all duration-300">
                    <div
                      className={`h-full rounded-full bg-gradient-to-r ${data.color} transition-all duration-700 ease-out group-hover:scale-105 relative group-hover:shadow-lg`}
                      style={{ width: `${Math.min(data.sales, 120)}%` }}
                    >
                      <div className="absolute inset-0 bg-white opacity-20 rounded-full group-hover:animate-pulse"></div>
                      {/* Tooltip on hover */}
                      <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap">
                        {data.sales}% • ₱{data.revenue.toLocaleString()}
                      </div>
                    </div>
                    {/* Target line */}
                    <div className="absolute top-0 h-full w-0.5 bg-gray-400 group-hover:bg-gray-600 transition-colors duration-300" style={{ left: '100%' }}></div>
                  </div>
                </div>
                {/* Performance indicator */}
                <div className="mt-2 flex items-center justify-between opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="flex items-center space-x-2">
                    {data.sales >= data.target ? (
                      <div className="flex items-center space-x-1 text-green-600">
                        <TrendingUp className="h-3 w-3" />
                        <span className="text-xs font-medium">Target Met</span>
                      </div>
                    ) : (
                      <div className="flex items-center space-x-1 text-orange-600">
                        <TrendingDown className="h-3 w-3" />
                        <span className="text-xs font-medium">Below Target</span>
                      </div>
                    )}
                  </div>
                  <div className="text-xs text-gray-500">
                    {data.sales >= data.target ? '+' : ''}{data.sales - data.target}%
                  </div>
                </div>
              </div>
            ))}
          </div>
          <div className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-green-50 rounded-xl">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-semibold text-gray-700">Weekly Average</p>
                <p className="text-xs text-gray-600">Performance metric</p>
              </div>
              <div className="text-right">
                <p className="text-lg font-bold text-blue-600">89.6%</p>
                <p className="text-xs text-green-600 flex items-center">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +5.2% from last week
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Top Products */}
        <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow duration-300">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-xl font-bold text-gray-900">Top Selling Products</h3>
              <p className="text-sm text-gray-600 mt-1">Best performers this week</p>
            </div>
            <div className="bg-gradient-to-r from-purple-500 to-pink-500 p-2 rounded-lg">
              <PieChart className="h-5 w-5 text-white" />
            </div>
          </div>
          <div className="space-y-3">
            {[
              { name: 'Lucky Me Instant Noodles', sales: 45, revenue: 675, trend: 'up', percentage: 85, changePercent: 12 },
              { name: 'Coca-Cola 350ml', sales: 38, revenue: 950, trend: 'up', percentage: 72, changePercent: 8 },
              { name: 'Skyflakes Crackers', sales: 32, revenue: 480, trend: 'down', percentage: 60, changePercent: 5 },
              { name: 'Maggi Magic Sarap', sales: 28, revenue: 420, trend: 'up', percentage: 52, changePercent: 15 },
              { name: 'Tide Detergent Powder', sales: 25, revenue: 1250, trend: 'up', percentage: 47, changePercent: 9 },
            ].map((product, index) => (
              <div key={index} className="group relative p-4 bg-gradient-to-r from-gray-50 to-gray-100 hover:from-purple-50 hover:to-pink-50 rounded-xl transition-all duration-300 border border-transparent hover:border-purple-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className={`w-10 h-10 rounded-xl flex items-center justify-center font-bold text-white shadow-lg ${
                      index === 0 ? 'bg-gradient-to-br from-yellow-400 to-orange-500' :
                      index === 1 ? 'bg-gradient-to-br from-gray-400 to-gray-600' :
                      index === 2 ? 'bg-gradient-to-br from-amber-600 to-yellow-700' :
                      'bg-gradient-to-br from-blue-500 to-blue-600'
                    }`}>
                      <span className="text-sm">{index + 1}</span>
                    </div>
                    <div className="flex-1">
                      <div className="font-semibold text-gray-900 text-sm group-hover:text-purple-900 transition-colors duration-300">
                        {product.name}
                      </div>
                      <div className="flex items-center space-x-3 mt-1">
                        <span className="text-xs text-gray-500">{product.sales} units sold</span>
                        <div className="flex items-center space-x-1">
                          {product.trend === 'up' ? (
                            <TrendingUp className="h-3 w-3 text-green-500" />
                          ) : (
                            <TrendingDown className="h-3 w-3 text-red-500" />
                          )}
                          <span className={`text-xs font-medium ${
                            product.trend === 'up' ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {product.trend === 'up' ? '+' : '-'}{product.changePercent || 8}%
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-bold text-gray-900 text-lg">₱{product.revenue.toLocaleString()}</div>
                    <div className="text-xs text-gray-500">revenue</div>
                  </div>
                </div>
                {/* Progress bar */}
                <div className="mt-3">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-xs text-gray-500">Performance</span>
                    <span className="text-xs font-medium text-gray-700">{product.percentage}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full transition-all duration-500 ease-out"
                      style={{ width: `${product.percentage}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
          <div className="mt-6 p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-semibold text-gray-700">Total Products Sold</p>
                <p className="text-xs text-gray-600">This week</p>
              </div>
              <div className="text-right">
                <p className="text-lg font-bold text-purple-600">168 units</p>
                <p className="text-xs text-green-600 flex items-center">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +12% from last week
                </p>
              </div>
            </div>
          </div>
        </div>
        </div>
      </div>

      {/* Enhanced Alerts & Business Intelligence Section */}
      <div className="w-full mobile-content">
        <div className="mt-8">
        <div className="bg-gradient-to-br from-amber-50 via-orange-50 to-red-50 border border-amber-200 rounded-2xl p-6 shadow-lg">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-4">
              <div className="bg-gradient-to-br from-amber-500 to-orange-600 p-3 rounded-xl shadow-lg">
                <AlertTriangle className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="text-2xl font-bold text-gray-900">Business Intelligence</h3>
                <p className="text-sm text-gray-600 mt-1">
                  Critical alerts and actionable insights for your store
                </p>
              </div>
            </div>
            <div className="bg-white p-2 rounded-lg shadow-sm">
              <Target className="h-5 w-5 text-amber-600" />
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mb-6">
            <div className="group bg-white p-6 rounded-2xl border border-amber-200 hover:shadow-xl hover:border-amber-300 transition-all duration-300 relative overflow-hidden">
              <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full opacity-10 transform translate-x-8 -translate-y-8"></div>
              <div className="relative">
                <div className="flex items-center justify-between mb-4">
                  <div className="text-sm font-bold text-gray-900">Low Stock Alert</div>
                  <div className="bg-gradient-to-br from-yellow-500 to-orange-600 p-2 rounded-lg group-hover:scale-110 transition-transform duration-300">
                    <Package className="h-4 w-4 text-white" />
                  </div>
                </div>
                <div className="text-3xl font-bold text-amber-600 mb-2">3</div>
                <div className="text-xs text-gray-600 mb-4">Items need immediate restocking</div>
                <div className="flex items-center justify-between">
                  <div className="text-xs text-amber-700 font-medium">Critical Priority</div>
                  <button className="text-xs text-amber-700 hover:text-amber-900 font-bold bg-amber-100 px-3 py-1 rounded-full hover:bg-amber-200 transition-colors duration-300">
                    View Details →
                  </button>
                </div>
              </div>
            </div>

            <div className="group bg-white p-6 rounded-2xl border border-red-200 hover:shadow-xl hover:border-red-300 transition-all duration-300 relative overflow-hidden">
              <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-red-400 to-red-600 rounded-full opacity-10 transform translate-x-8 -translate-y-8"></div>
              <div className="relative">
                <div className="flex items-center justify-between mb-4">
                  <div className="text-sm font-bold text-gray-900">Overdue Debts</div>
                  <div className="bg-gradient-to-br from-red-500 to-red-600 p-2 rounded-lg group-hover:scale-110 transition-transform duration-300">
                    <CreditCard className="h-4 w-4 text-white" />
                  </div>
                </div>
                <div className="text-3xl font-bold text-red-600 mb-2">2</div>
                <div className="text-xs text-gray-600 mb-4">Customers with overdue payments</div>
                <div className="flex items-center justify-between">
                  <div className="text-xs text-red-700 font-medium">₱1,250 Total</div>
                  <button className="text-xs text-red-700 hover:text-red-900 font-bold bg-red-100 px-3 py-1 rounded-full hover:bg-red-200 transition-colors duration-300">
                    Follow Up →
                  </button>
                </div>
              </div>
            </div>

            <div className="group bg-white p-6 rounded-2xl border border-green-200 hover:shadow-xl hover:border-green-300 transition-all duration-300 relative overflow-hidden">
              <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-green-400 to-emerald-600 rounded-full opacity-10 transform translate-x-8 -translate-y-8"></div>
              <div className="relative">
                <div className="flex items-center justify-between mb-4">
                  <div className="text-sm font-bold text-gray-900">Today&apos;s Performance</div>
                  <div className="bg-gradient-to-br from-green-500 to-emerald-600 p-2 rounded-lg group-hover:scale-110 transition-transform duration-300">
                    <TrendingUp className="h-4 w-4 text-white" />
                  </div>
                </div>
                <div className="text-3xl font-bold text-green-600 mb-2">₱2,450</div>
                <div className="text-xs text-gray-600 mb-4">Revenue generated today</div>
                <div className="flex items-center justify-between">
                  <div className="text-xs text-green-700 font-medium">+15% vs yesterday</div>
                  <button className="text-xs text-green-700 hover:text-green-900 font-bold bg-green-100 px-3 py-1 rounded-full hover:bg-green-200 transition-colors duration-300">
                    View Report →
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Enhanced Recent Notifications */}
          <div className="bg-white rounded-2xl border border-amber-200 p-6 shadow-sm">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h4 className="text-lg font-bold text-gray-900">Live Notifications</h4>
                <p className="text-sm text-gray-600 mt-1">Real-time updates from your store</p>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-xs text-gray-500">Live</span>
              </div>
            </div>
            <div className="space-y-4">
              <div className="group flex items-start space-x-4 p-4 bg-gradient-to-r from-red-50 to-orange-50 hover:from-red-100 hover:to-orange-100 rounded-xl transition-all duration-300 border border-red-200">
                <div className="flex-shrink-0 w-3 h-3 bg-gradient-to-br from-red-400 to-red-600 rounded-full mt-2 shadow-lg"></div>
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-semibold text-gray-900">Critical Stock Alert</span>
                    <span className="text-xs text-gray-500 bg-white px-2 py-1 rounded-full">2 hours ago</span>
                  </div>
                  <p className="text-sm text-gray-700 mt-1">Lucky Me Instant Noodles is running low (5 units left)</p>
                  <div className="mt-2">
                    <button className="text-xs text-red-700 hover:text-red-900 font-medium bg-red-100 px-3 py-1 rounded-full hover:bg-red-200 transition-colors duration-300">
                      Restock Now
                    </button>
                  </div>
                </div>
              </div>

              <div className="group flex items-start space-x-4 p-4 bg-gradient-to-r from-yellow-50 to-amber-50 hover:from-yellow-100 hover:to-amber-100 rounded-xl transition-all duration-300 border border-yellow-200">
                <div className="flex-shrink-0 w-3 h-3 bg-gradient-to-br from-yellow-400 to-amber-600 rounded-full mt-2 shadow-lg"></div>
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-semibold text-gray-900">Payment Reminder</span>
                    <span className="text-xs text-gray-500 bg-white px-2 py-1 rounded-full">4 hours ago</span>
                  </div>
                  <p className="text-sm text-gray-700 mt-1">Customer payment reminder sent to Maria Santos</p>
                  <div className="mt-2">
                    <button className="text-xs text-amber-700 hover:text-amber-900 font-medium bg-amber-100 px-3 py-1 rounded-full hover:bg-amber-200 transition-colors duration-300">
                      View Details
                    </button>
                  </div>
                </div>
              </div>

              <div className="group flex items-start space-x-4 p-4 bg-gradient-to-r from-green-50 to-emerald-50 hover:from-green-100 hover:to-emerald-100 rounded-xl transition-all duration-300 border border-green-200">
                <div className="flex-shrink-0 w-3 h-3 bg-gradient-to-br from-green-400 to-emerald-600 rounded-full mt-2 shadow-lg"></div>
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-semibold text-gray-900">Inventory Update</span>
                    <span className="text-xs text-gray-500 bg-white px-2 py-1 rounded-full">6 hours ago</span>
                  </div>
                  <p className="text-sm text-gray-700 mt-1">New product &ldquo;Pancit Canton&rdquo; added to inventory</p>
                  <div className="mt-2">
                    <button className="text-xs text-green-700 hover:text-green-900 font-medium bg-green-100 px-3 py-1 rounded-full hover:bg-green-200 transition-colors duration-300">
                      View Product
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-6 pt-4 border-t border-gray-200">
              <button className="w-full text-sm font-medium text-gray-600 hover:text-gray-900 py-2 px-4 bg-gray-50 hover:bg-gray-100 rounded-xl transition-colors duration-300">
                View All Notifications
              </button>
            </div>
          </div>
        </div>
        </div>
      </div>

      {/* Professional Dashboard Widgets - Mobile Optimized */}
      <div className="w-full mobile-content">
        <div className="mt-6 sm:mt-8 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
        {/* Business Calendar Widget */}
        <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow duration-300">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-lg font-bold text-gray-900">Business Calendar</h3>
              <p className="text-sm text-gray-600 mt-1">Important dates & events</p>
            </div>
            <div className="bg-gradient-to-r from-indigo-500 to-purple-600 p-2 rounded-lg">
              <Calendar className="h-5 w-5 text-white" />
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                <div>
                  <p className="text-sm font-semibold text-gray-900">Inventory Restock</p>
                  <p className="text-xs text-gray-600">Tomorrow, 9:00 AM</p>
                </div>
              </div>
              <div className="text-xs text-blue-600 font-medium">Due</div>
            </div>

            <div className="flex items-center justify-between p-3 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl border border-green-200">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <div>
                  <p className="text-sm font-semibold text-gray-900">Monthly Report</p>
                  <p className="text-xs text-gray-600">June 30, 2025</p>
                </div>
              </div>
              <div className="text-xs text-green-600 font-medium">Upcoming</div>
            </div>

            <div className="flex items-center justify-between p-3 bg-gradient-to-r from-amber-50 to-yellow-50 rounded-xl border border-amber-200">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-amber-500 rounded-full"></div>
                <div>
                  <p className="text-sm font-semibold text-gray-900">Payment Follow-up</p>
                  <p className="text-xs text-gray-600">Weekly task</p>
                </div>
              </div>
              <div className="text-xs text-amber-600 font-medium">Recurring</div>
            </div>
          </div>

          <button className="w-full mt-4 text-sm font-medium text-gray-600 hover:text-gray-900 py-2 px-4 bg-gray-50 hover:bg-gray-100 rounded-xl transition-colors duration-300">
            View Full Calendar
          </button>
        </div>

        {/* Quick Stats Widget */}
        <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow duration-300">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-lg font-bold text-gray-900">Quick Insights</h3>
              <p className="text-sm text-gray-600 mt-1">Key performance metrics</p>
            </div>
            <div className="bg-gradient-to-r from-green-500 to-teal-600 p-2 rounded-lg">
              <BarChart3 className="h-5 w-5 text-white" />
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="bg-blue-100 p-2 rounded-lg">
                  <TrendingUp className="h-4 w-4 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm font-semibold text-gray-900">Daily Average</p>
                  <p className="text-xs text-gray-600">Sales performance</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-lg font-bold text-blue-600">₱1,850</p>
                <p className="text-xs text-green-600">+8.5%</p>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="bg-purple-100 p-2 rounded-lg">
                  <Users className="h-4 w-4 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm font-semibold text-gray-900">Active Customers</p>
                  <p className="text-xs text-gray-600">This month</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-lg font-bold text-purple-600">28</p>
                <p className="text-xs text-green-600">+12%</p>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="bg-orange-100 p-2 rounded-lg">
                  <Package className="h-4 w-4 text-orange-600" />
                </div>
                <div>
                  <p className="text-sm font-semibold text-gray-900">Inventory Turnover</p>
                  <p className="text-xs text-gray-600">Monthly rate</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-lg font-bold text-orange-600">2.4x</p>
                <p className="text-xs text-green-600">+5%</p>
              </div>
            </div>
          </div>

          <div className="mt-6 p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-semibold text-gray-700">Store Health Score</p>
                <p className="text-xs text-gray-600">Overall performance</p>
              </div>
              <div className="text-right">
                <p className="text-2xl font-bold text-green-600">92%</p>
                <p className="text-xs text-green-600">Excellent</p>
              </div>
            </div>
          </div>
        </div>

        {/* Store Status Widget */}
        <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow duration-300">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-lg font-bold text-gray-900">Store Status</h3>
              <p className="text-sm text-gray-600 mt-1">Current operations</p>
            </div>
            <div className="bg-gradient-to-r from-emerald-500 to-green-600 p-2 rounded-lg">
              <Activity className="h-5 w-5 text-white" />
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl border border-green-200">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                <div>
                  <p className="text-sm font-bold text-gray-900">Store Status</p>
                  <p className="text-xs text-gray-600">Currently open</p>
                </div>
              </div>
              <div className="text-sm font-bold text-green-600">ONLINE</div>
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">System Health</span>
                <div className="flex items-center space-x-2">
                  <div className="w-16 bg-gray-200 rounded-full h-2">
                    <div className="bg-green-500 h-2 rounded-full" style={{ width: '95%' }}></div>
                  </div>
                  <span className="text-xs font-medium text-green-600">95%</span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Inventory Status</span>
                <div className="flex items-center space-x-2">
                  <div className="w-16 bg-gray-200 rounded-full h-2">
                    <div className="bg-yellow-500 h-2 rounded-full" style={{ width: '78%' }}></div>
                  </div>
                  <span className="text-xs font-medium text-yellow-600">78%</span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Customer Satisfaction</span>
                <div className="flex items-center space-x-2">
                  <div className="w-16 bg-gray-200 rounded-full h-2">
                    <div className="bg-blue-500 h-2 rounded-full" style={{ width: '88%' }}></div>
                  </div>
                  <span className="text-xs font-medium text-blue-600">88%</span>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-semibold text-gray-700">Today&apos;s Goal</p>
                <p className="text-xs text-gray-600">Sales target progress</p>
              </div>
              <div className="text-right">
                <p className="text-lg font-bold text-blue-600">₱2,450</p>
                <p className="text-xs text-blue-600">of ₱3,000</p>
              </div>
            </div>
            <div className="mt-3">
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div className="bg-gradient-to-r from-blue-500 to-indigo-600 h-3 rounded-full" style={{ width: '82%' }}></div>
              </div>
              <p className="text-xs text-gray-600 mt-1">82% completed</p>
            </div>
          </div>
        </div>
        </div>
      </div>

      {/* Performance Monitor - Client Side Only */}
      {isHydrated && <PerformanceMonitor />}
    </DashboardLayout>
  )
}
